// Simple JavaScript for the static site
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if(targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if(targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add active class to nav items on scroll
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('nav ul li a');
    
    window.addEventListener('scroll', () => {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if(pageYOffset >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if(link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
});

// Resources section functionality
function showCategory(categoryId) {
    // Hide all category contents
    const allCategories = document.querySelectorAll('.category-content');
    allCategories.forEach(category => {
        category.classList.remove('active');
    });
    
    // Remove active class from all tabs
    const allTabs = document.querySelectorAll('.tab-btn');
    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected category
    const selectedCategory = document.getElementById(categoryId);
    if (selectedCategory) {
        selectedCategory.classList.add('active');
    }
    
    // Add active class to clicked tab
    const clickedTab = event.target;
    clickedTab.classList.add('active');
}

// Copy RSS URL to clipboard
function copyRSSUrl() {
    const rssUrl = window.location.origin + '/rss.xml';
    
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(rssUrl).then(() => {
            showCopyFeedback('RSS URL copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy RSS URL: ', err);
            fallbackCopyTextToClipboard(rssUrl);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(rssUrl);
    }
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopyFeedback('RSS URL copied to clipboard!');
        } else {
            showCopyFeedback('Failed to copy RSS URL', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showCopyFeedback('Failed to copy RSS URL', 'error');
    }
    
    document.body.removeChild(textArea);
}

// Show copy feedback to user
function showCopyFeedback(message, type = 'success') {
    // Remove existing feedback if any
    const existingFeedback = document.querySelector('.copy-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = `copy-feedback ${type}`;
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : '#f44336'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(feedback);
    
    // Remove feedback after 3 seconds
    setTimeout(() => {
        feedback.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 300);
    }, 3000);
}

// Show RSS subscription options modal
function showRSSOptions() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

// Close RSS modal
function closeRSSModal() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }
}

// Copy RSS URL from input field
function copyFromInput() {
    const input = document.getElementById('rss-url-input');
    if (input) {
        input.select();
        input.setSelectionRange(0, 99999); // For mobile devices
        
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(input.value).then(() => {
                showCopyFeedback('RSS URL copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy RSS URL: ', err);
                fallbackCopyTextToClipboard(input.value);
            });
        } else {
            fallbackCopyTextToClipboard(input.value);
        }
    }
}

// Load RSS feed dynamically
async function loadRSSFeed() {
    const feedPreview = document.getElementById('rss-feed-preview');
    if (!feedPreview) return;

    try {
        // Fetch the RSS feed
        const response = await fetch('/rss.xml');
        const xmlText = await response.text();
        
        // Parse the XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
        
        // Extract items from RSS
        const items = xmlDoc.querySelectorAll('item');
        
        // Clear existing content
        feedPreview.innerHTML = '';
        
        // Display first 2 items (most recent)
        const itemsToShow = Array.from(items).slice(0, 2);
        
        itemsToShow.forEach(item => {
            const title = item.querySelector('title')?.textContent || 'No title';
            const pubDate = item.querySelector('pubDate')?.textContent || '';
            const description = item.querySelector('description')?.textContent || '';
            const link = item.querySelector('link')?.textContent || '#';
            
            // Format date
            const formattedDate = formatRSSDate(pubDate);
            
            // Extract plain text from description (remove HTML)
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = description;
            const plainDescription = tempDiv.textContent || tempDiv.innerText || '';
            
            // Truncate description to ~100 characters
            const truncatedDescription = plainDescription.length > 100 
                ? plainDescription.substring(0, 100) + '...' 
                : plainDescription;
            
            // Create feed item element
            const feedItem = document.createElement('div');
            feedItem.className = 'feed-item';
            feedItem.innerHTML = `
                <h5><a href="${link}" target="_blank">${title}</a></h5>
                <p class="feed-date">${formattedDate}</p>
                <p class="feed-excerpt">${truncatedDescription}</p>
            `;
            
            feedPreview.appendChild(feedItem);
        });
        
        console.log('RSS feed loaded successfully');
        
    } catch (error) {
        console.error('Failed to load RSS feed:', error);
        
        // Fallback to hardcoded content if RSS loading fails
        feedPreview.innerHTML = `
            <div class="feed-item">
                <h5>Building Scalable Microservices with Spring Boot</h5>
                <p class="feed-date">January 15, 2025</p>
                <p class="feed-excerpt">Learn how to design and implement microservices architecture using Spring Boot, Docker, and Kubernetes...</p>
            </div>
            <div class="feed-item">
                <h5>DevOps Best Practices for Backend Developers</h5>
                <p class="feed-date">January 10, 2025</p>
                <p class="feed-excerpt">Essential DevOps practices every backend developer should know, from CI/CD to monitoring...</p>
            </div>
        `;
    }
}

// Format RSS date to readable format
function formatRSSDate(rssDate) {
    if (!rssDate) return '';
    
    try {
        const date = new Date(rssDate);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (error) {
        return rssDate; // Return original if parsing fails
    }
}

// Close modal when clicking outside of it
document.addEventListener('click', function(event) {
    const modal = document.getElementById('rss-modal');
    if (modal && event.target === modal) {
        closeRSSModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeRSSModal();
    }
});

// Initialize RSS feed on page load
document.addEventListener('DOMContentLoaded', function() {
    loadRSSFeed();
    initDinoGame();
});

// Chrome Dinosaur Game Implementation
class DinoGame {
    constructor() {
        this.canvas = document.getElementById('dino-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameRunning = false;
        this.gameOver = false;
        this.score = 0;
        this.highScore = localStorage.getItem('dinoHighScore') || 0;

        // Game objects
        this.dino = {
            x: 50,
            y: 150,
            width: 20,
            height: 20,
            velocityY: 0,
            jumping: false,
            grounded: true
        };

        this.obstacles = [];
        this.clouds = [];
        this.ground = { x: 0 };

        // Game settings - Much more beginner-friendly
        this.gravity = 0.5; // Reduced gravity for easier jumping
        this.jumpPower = -14; // More jump power
        this.initialGameSpeed = 1.5; // Much slower initial speed
        this.gameSpeed = this.initialGameSpeed;
        this.maxGameSpeed = 4; // Lower maximum speed
        this.initialObstacleSpawnRate = 0.003; // Extremely low initial spawn rate
        this.maxObstacleSpawnRate = 0.015; // Lower maximum spawn rate
        this.obstacleSpawnRate = this.initialObstacleSpawnRate;
        this.cloudSpawnRate = 0.005;
        this.minObstacleDistance = 350; // Much larger minimum distance between obstacles
        this.lastObstacleX = -this.minObstacleDistance; // Track last obstacle position

        // Difficulty progression settings
        this.difficultyIncreaseRate = 0.0001; // How fast difficulty increases
        this.collisionPadding = 6; // Much more forgiving collision detection initially
        this.obstacleTimer = 0; // Timer to control obstacle spawning
        this.obstacleInterval = 180; // Frames between obstacles (3 seconds at 60fps)

        this.setupCanvas();
        this.setupEventListeners();
        this.updateHighScore();
        this.gameLoop();
    }

    setupCanvas() {
        // Set actual canvas size
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;

        // Initialize ground position
        this.ground.y = this.canvas.height - 20;
        this.dino.y = this.ground.y - this.dino.height;

        // Generate initial clouds
        for (let i = 0; i < 3; i++) {
            this.clouds.push({
                x: Math.random() * this.canvas.width,
                y: 20 + Math.random() * 50,
                width: 30 + Math.random() * 20,
                height: 15 + Math.random() * 10
            });
        }
    }

    setupEventListeners() {
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.jump();
            }
        });

        // Mouse/touch controls
        this.canvas.addEventListener('click', () => {
            this.jump();
        });

        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.jump();
        });

        // Game control buttons
        document.getElementById('start-game').addEventListener('click', () => {
            this.startGame();
        });

        document.getElementById('restart-game').addEventListener('click', () => {
            this.restartGame();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.setupCanvas();
        });
    }

    jump() {
        if (!this.gameRunning && !this.gameOver) {
            this.startGame();
            return;
        }

        if (this.gameRunning && this.dino.grounded) {
            this.dino.velocityY = this.jumpPower;
            this.dino.jumping = true;
            this.dino.grounded = false;
        }
    }

    startGame() {
        this.gameRunning = true;
        this.gameOver = false;
        this.score = 0;
        this.obstacles = [];

        // Reset difficulty to beginner-friendly settings
        this.gameSpeed = this.initialGameSpeed;
        this.obstacleSpawnRate = this.initialObstacleSpawnRate;
        this.collisionPadding = 6; // Much more forgiving initially
        this.lastObstacleX = -this.minObstacleDistance;
        this.obstacleTimer = 0;
        this.obstacleInterval = 180; // Start with 3 seconds between obstacles

        this.dino.y = this.ground.y - this.dino.height;
        this.dino.velocityY = 0;
        this.dino.jumping = false;
        this.dino.grounded = true;

        document.getElementById('start-game').style.display = 'none';
        document.getElementById('game-over').style.display = 'none';

        // Add game active animation
        this.canvas.classList.add('game-active');
        setTimeout(() => {
            this.canvas.classList.remove('game-active');
        }, 500);

        this.updateScore();
    }

    restartGame() {
        this.startGame();
    }

    updateScore() {
        document.getElementById('score').textContent = Math.floor(this.score);

        if (this.score > this.highScore) {
            this.highScore = Math.floor(this.score);
            localStorage.setItem('dinoHighScore', this.highScore);
            this.updateHighScore();
        }

        // Update difficulty level display
        this.updateDifficultyLevel();
    }

    updateDifficultyLevel() {
        const difficultyElement = document.getElementById('difficulty-level');
        if (!difficultyElement) return;

        const score = Math.floor(this.score);
        let level = 'Beginner';
        let color = '#28a745'; // Green

        if (score >= 1500) {
            level = 'Expert';
            color = '#dc3545'; // Red
        } else if (score >= 800) {
            level = 'Advanced';
            color = '#fd7e14'; // Orange
        } else if (score >= 300) {
            level = 'Intermediate';
            color = '#ffc107'; // Yellow
        }

        difficultyElement.textContent = level;
        difficultyElement.style.color = color;
    }

    updateHighScore() {
        document.getElementById('high-score').textContent = this.highScore;
    }

    endGame() {
        this.gameRunning = false;
        this.gameOver = true;

        document.getElementById('final-score').textContent = Math.floor(this.score);
        document.getElementById('game-over').style.display = 'block';

        // Show start button again after a delay
        setTimeout(() => {
            if (this.gameOver) {
                document.getElementById('start-game').style.display = 'inline-block';
            }
        }, 3000);
    }

    update() {
        if (!this.gameRunning) return;

        // Update score
        this.score += 0.1;
        this.updateScore();

        // Progressive difficulty increase - extremely gradual
        const difficultyProgress = Math.min(this.score / 2000, 1); // Normalize to 0-1 over 2000 points (much slower)

        // Gradually increase game speed
        this.gameSpeed = this.initialGameSpeed + (this.maxGameSpeed - this.initialGameSpeed) * difficultyProgress;

        // Gradually decrease obstacle interval (increase frequency)
        const minInterval = 90; // Minimum 1.5 seconds between obstacles
        const maxInterval = 180; // Maximum 3 seconds between obstacles
        this.obstacleInterval = maxInterval - (maxInterval - minInterval) * difficultyProgress;

        // Gradually make collision detection less forgiving (but still generous)
        this.collisionPadding = 6 - (3 * difficultyProgress); // From 6 to 3 padding (still forgiving)

        // Update dino physics with better feel
        this.dino.velocityY += this.gravity;
        this.dino.y += this.dino.velocityY;

        // Ground collision with better landing detection
        if (this.dino.y >= this.ground.y - this.dino.height) {
            this.dino.y = this.ground.y - this.dino.height;
            this.dino.velocityY = 0;
            this.dino.jumping = false;
            this.dino.grounded = true;
        } else {
            this.dino.grounded = false;
        }

        // Update ground
        this.ground.x -= this.gameSpeed;
        if (this.ground.x <= -50) {
            this.ground.x = 0;
        }

        // Timer-based obstacle spawning for consistent spacing
        this.obstacleTimer++;
        if (this.obstacleTimer >= this.obstacleInterval) {
            // Progressive obstacle size - start smaller
            const baseHeight = 20;
            const maxHeight = 30;
            const heightProgress = Math.min(this.score / 1000, 1);
            const obstacleHeight = baseHeight + (maxHeight - baseHeight) * heightProgress;

            this.obstacles.push({
                x: this.canvas.width,
                y: this.ground.y - obstacleHeight,
                width: 12, // Slightly narrower for easier avoidance
                height: obstacleHeight
            });
            this.obstacleTimer = 0; // Reset timer
            this.lastObstacleX = this.canvas.width;
        }

        // Update obstacles
        this.obstacles.forEach((obstacle, index) => {
            obstacle.x -= this.gameSpeed;

            // Check if player successfully jumped over obstacle (bonus points)
            if (!obstacle.passed && obstacle.x + obstacle.width < this.dino.x) {
                obstacle.passed = true;
                this.score += 5; // Bonus points for successful jump
            }

            // Remove off-screen obstacles and update last obstacle position
            if (obstacle.x + obstacle.width < 0) {
                this.obstacles.splice(index, 1);
                // Reset last obstacle position when obstacles are removed
                if (this.obstacles.length === 0) {
                    this.lastObstacleX = -this.minObstacleDistance;
                }
            }

            // Collision detection with progressive padding
            if (this.checkCollision(this.dino, obstacle)) {
                this.endGame();
            }
        });

        // Update last obstacle position based on existing obstacles
        if (this.obstacles.length > 0) {
            this.lastObstacleX = Math.max(...this.obstacles.map(obs => obs.x));
        }

        // Spawn clouds
        if (Math.random() < this.cloudSpawnRate) {
            this.clouds.push({
                x: this.canvas.width,
                y: 20 + Math.random() * 50,
                width: 30 + Math.random() * 20,
                height: 15 + Math.random() * 10
            });
        }

        // Update clouds
        this.clouds.forEach((cloud, index) => {
            cloud.x -= this.gameSpeed * 0.3; // Clouds move slower

            // Remove off-screen clouds
            if (cloud.x + cloud.width < 0) {
                this.clouds.splice(index, 1);
            }
        });
    }

    checkCollision(rect1, rect2) {
        // Use progressive collision padding for difficulty scaling
        const padding = this.collisionPadding;
        return rect1.x + padding < rect2.x + rect2.width &&
               rect1.x + rect1.width - padding > rect2.x &&
               rect1.y + padding < rect2.y + rect2.height &&
               rect1.y + rect1.height - padding > rect2.y;
    }

    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw sky gradient
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(0.7, '#87CEEB');
        gradient.addColorStop(1, '#DEB887');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw clouds
        this.ctx.fillStyle = '#FFFFFF';
        this.clouds.forEach(cloud => {
            this.drawCloud(cloud.x, cloud.y, cloud.width, cloud.height);
        });

        // Draw ground pattern
        this.ctx.fillStyle = '#8B7355';
        this.ctx.fillRect(0, this.ground.y, this.canvas.width, 20);

        // Draw ground texture
        this.ctx.fillStyle = '#A0522D';
        for (let x = this.ground.x; x < this.canvas.width + 50; x += 50) {
            this.ctx.fillRect(x, this.ground.y + 2, 2, 16);
            this.ctx.fillRect(x + 25, this.ground.y + 2, 2, 16);
        }

        // Draw dino with different appearance when jumping
        if (this.dino.jumping) {
            this.ctx.fillStyle = '#32CD32'; // Brighter green when jumping
        } else {
            this.ctx.fillStyle = '#2F4F2F';
        }
        this.ctx.fillRect(this.dino.x, this.dino.y, this.dino.width, this.dino.height);

        // Draw dino details
        if (this.dino.jumping) {
            this.ctx.fillStyle = '#90EE90'; // Lighter green when jumping
        } else {
            this.ctx.fillStyle = '#228B22';
        }
        this.ctx.fillRect(this.dino.x + 2, this.dino.y + 2, this.dino.width - 4, this.dino.height - 4);

        // Draw dino eye
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillRect(this.dino.x + 14, this.dino.y + 4, 3, 3);
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(this.dino.x + 15, this.dino.y + 5, 1, 1);

        // Draw obstacles (cacti)
        this.ctx.fillStyle = '#228B22';
        this.obstacles.forEach(obstacle => {
            this.drawCactus(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
        });
    }

    drawCloud(x, y, width, height) {
        this.ctx.beginPath();
        this.ctx.arc(x, y + height/2, height/2, 0, Math.PI * 2);
        this.ctx.arc(x + width/3, y + height/2, height/2, 0, Math.PI * 2);
        this.ctx.arc(x + 2*width/3, y + height/2, height/2, 0, Math.PI * 2);
        this.ctx.arc(x + width, y + height/2, height/2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawCactus(x, y, width, height) {
        // Main cactus body
        this.ctx.fillRect(x + width/3, y, width/3, height);

        // Left arm
        this.ctx.fillRect(x, y + height/3, width/3, width/4);
        this.ctx.fillRect(x, y + height/3, width/4, height/2);

        // Right arm
        this.ctx.fillRect(x + 2*width/3, y + height/2, width/3, width/4);
        this.ctx.fillRect(x + 3*width/4, y + height/2, width/4, height/3);

        // Cactus spikes
        this.ctx.fillStyle = '#006400';
        for (let i = 0; i < 3; i++) {
            this.ctx.fillRect(x + width/3 - 1, y + i * height/3, 1, 2);
            this.ctx.fillRect(x + 2*width/3, y + i * height/3, 1, 2);
        }
        this.ctx.fillStyle = '#228B22';
    }

    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Initialize the dino game
function initDinoGame() {
    // Check if canvas exists before initializing
    const canvas = document.getElementById('dino-canvas');
    if (canvas) {
        new DinoGame();
    }
}