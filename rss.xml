<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
        <title>Nob <PERSON><PERSON><PERSON> - Senior Backend Developer Blog</title>
        <description>Technical insights, tutorials, and thoughts on backend development, DevOps, and software architecture</description>
        <link>https://nobhokleng.dev</link>
        <atom:link href="https://nobhokleng.dev/rss.xml" rel="self" type="application/rss+xml"/>
        <language>en-us</language>
        <lastBuildDate>Wed, 15 Jan 2025 10:00:00 GMT</lastBuildDate>
        <pubDate>Wed, 15 Jan 2025 10:00:00 GMT</pubDate>
        <ttl>1440</ttl>
        <managingEditor><EMAIL> (Nob Hokleng)</managingEditor>
        <webMaster><EMAIL> (Nob Hokleng)</webMaster>
        <category>Technology</category>
        <category>Backend Development</category>
        <category>DevOps</category>
        <category>Software Architecture</category>
        
        <item>
            <title>Building Scalable Microservices with Spring Boot</title>
            <description><![CDATA[
                <p>Learn how to design and implement microservices architecture using Spring Boot, Docker, and Kubernetes. This comprehensive guide covers best practices for building scalable, maintainable microservices that can handle enterprise-level traffic.</p>
                
                <h3>Key Topics Covered:</h3>
                <ul>
                    <li>Microservices design patterns</li>
                    <li>Service discovery and load balancing</li>
                    <li>Database per service pattern</li>
                    <li>API Gateway implementation</li>
                    <li>Monitoring and observability</li>
                </ul>
                
                <p>Whether you're transitioning from monolithic architecture or starting fresh, this guide provides practical insights from 6+ years of backend development experience.</p>
            ]]></description>
            <link>https://nobhokleng.dev/blog/building-scalable-microservices-spring-boot</link>
            <guid isPermaLink="true">https://nobhokleng.dev/blog/building-scalable-microservices-spring-boot</guid>
            <pubDate>Wed, 15 Jan 2025 09:00:00 GMT</pubDate>
            <category>Spring Boot</category>
            <category>Microservices</category>
            <category>Docker</category>
            <category>Kubernetes</category>
            <author><EMAIL> (Nob Hokleng)</author>
        </item>
        
        <item>
            <title>DevOps Best Practices for Backend Developers</title>
            <description><![CDATA[
                <p>Essential DevOps practices every backend developer should know, from CI/CD pipelines to monitoring and infrastructure as code. Learn how to bridge the gap between development and operations for better software delivery.</p>
                
                <h3>What You'll Learn:</h3>
                <ul>
                    <li>Setting up robust CI/CD pipelines</li>
                    <li>Infrastructure as Code with Terraform</li>
                    <li>Container orchestration strategies</li>
                    <li>Monitoring and alerting best practices</li>
                    <li>Security considerations in DevOps</li>
                </ul>
                
                <p>Based on real-world experience implementing DevOps practices in production environments handling millions of requests.</p>
            ]]></description>
            <link>https://nobhokleng.dev/blog/devops-best-practices-backend-developers</link>
            <guid isPermaLink="true">https://nobhokleng.dev/blog/devops-best-practices-backend-developers</guid>
            <pubDate>Fri, 10 Jan 2025 14:30:00 GMT</pubDate>
            <category>DevOps</category>
            <category>CI/CD</category>
            <category>Infrastructure</category>
            <category>Monitoring</category>
            <author><EMAIL> (Nob Hokleng)</author>
        </item>
        
        <item>
            <title>Database Design Patterns for High-Performance Applications</title>
            <description><![CDATA[
                <p>Explore advanced database design patterns and optimization techniques for building high-performance applications. Learn when to use different database technologies and how to design schemas that scale.</p>
                
                <h3>Topics Covered:</h3>
                <ul>
                    <li>CQRS and Event Sourcing patterns</li>
                    <li>Database sharding strategies</li>
                    <li>Caching layers and Redis optimization</li>
                    <li>SQL vs NoSQL decision framework</li>
                    <li>Database migration best practices</li>
                </ul>
                
                <p>Real examples from optimizing databases handling terabytes of data and thousands of concurrent users.</p>
            ]]></description>
            <link>https://nobhokleng.dev/blog/database-design-patterns-high-performance</link>
            <guid isPermaLink="true">https://nobhokleng.dev/blog/database-design-patterns-high-performance</guid>
            <pubDate>Mon, 06 Jan 2025 11:15:00 GMT</pubDate>
            <category>Database Design</category>
            <category>Performance</category>
            <category>MySQL</category>
            <category>Redis</category>
            <category>MongoDB</category>
            <author><EMAIL> (Nob Hokleng)</author>
        </item>
        
        <item>
            <title>Leadership Lessons from 6 Years in Backend Development</title>
            <description><![CDATA[
                <p>Transitioning from individual contributor to technical leader requires more than just coding skills. Share insights on leading development teams, making architectural decisions, and mentoring junior developers.</p>
                
                <h3>Key Leadership Areas:</h3>
                <ul>
                    <li>Technical decision making and trade-offs</li>
                    <li>Code review best practices</li>
                    <li>Mentoring and knowledge sharing</li>
                    <li>Cross-team collaboration</li>
                    <li>Balancing technical debt and feature delivery</li>
                </ul>
                
                <p>Lessons learned from leading teams through complex technical challenges and scaling applications from startup to enterprise level.</p>
            ]]></description>
            <link>https://nobhokleng.dev/blog/leadership-lessons-backend-development</link>
            <guid isPermaLink="true">https://nobhokleng.dev/blog/leadership-lessons-backend-development</guid>
            <pubDate>Thu, 02 Jan 2025 16:45:00 GMT</pubDate>
            <category>Leadership</category>
            <category>Career Development</category>
            <category>Team Management</category>
            <category>Mentoring</category>
            <author><EMAIL> (Nob Hokleng)</author>
        </item>
    </channel>
</rss>